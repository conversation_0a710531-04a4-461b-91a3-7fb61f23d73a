import { createFileRoute, Outlet, Navigate } from '@tanstack/react-router'
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar'
import { DashboardHeader } from '@/components/dashboard/DashboardHeader'
import { SidebarInset, SidebarProvider } from '@/components/ui/Sidebar'
import { Authenticated, Unauthenticated, AuthLoading } from 'convex/react'
import { Spinner } from '@/components/ui/Spinner'

export const Route = createFileRoute('/dashboard')({
  component: DashboardLayout,
})

function DashboardLayout() {
  return (
    <>
      <AuthLoading>
        <div className="min-h-screen flex items-center justify-center">
          <Spinner variant="circle" size={48} className="text-primary" />
        </div>
      </AuthLoading>

      <Authenticated>
        <SidebarProvider>
          <DashboardSidebar variant="inset" collapsible="icon" />
          <SidebarInset>
            <DashboardHeader />
            <main className="flex flex-col my-4 mx-6">
              <Outlet />
            </main>
          </SidebarInset>
        </SidebarProvider>
      </Authenticated>

      <Unauthenticated>
        <Navigate to="/" replace />
      </Unauthenticated>
    </>
  )
}
