import { Separator } from '@/components/ui/Separator'
import { SidebarTrigger } from '@/components/ui/Sidebar'
import { AppUser } from '@/components/dashboard/AppUser'

export function DashboardHeader() {
  return (
    <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-2 lg:gap-2 lg:px-2">
        <SidebarTrigger className="ml-1" />
        <Separator orientation="vertical" className="mx-1 data-[orientation=vertical]:h-4" />
        <h1 className="text-xs md:text-sm font-medium">Dashboard</h1>
        <div className="ml-auto flex items-center gap-3">
          <AppUser />
        </div>
      </div>
    </header>
  )
}
