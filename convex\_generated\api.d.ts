/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as academicYears from "../academicYears.js";
import type * as auth from "../auth.js";
import type * as http from "../http.js";
import type * as majors from "../majors.js";
import type * as sections from "../sections.js";
import type * as strands from "../strands.js";
import type * as teachers from "../teachers.js";
import type * as tracks from "../tracks.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  academicYears: typeof academicYears;
  auth: typeof auth;
  http: typeof http;
  majors: typeof majors;
  sections: typeof sections;
  strands: typeof strands;
  teachers: typeof teachers;
  tracks: typeof tracks;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
