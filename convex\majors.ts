import { query } from './_generated/server'
import { v } from 'convex/values'

export const getAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query('majors').collect()
  },
})

export const getById = query({
  args: { id: v.id('majors') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})

export const getByStrand = query({
  args: { strandId: v.id('strands') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('majors')
      .withIndex('by_strand', (q) => q.eq('strandId', args.strandId))
      .collect()
  },
})

export const getByStrandWithDetails = query({
  args: { strandId: v.id('strands') },
  handler: async (ctx, args) => {
    const [majors, strand] = await Promise.all([
      ctx.db
        .query('majors')
        .withIndex('by_strand', (q) => q.eq('strandId', args.strandId))
        .collect(),
      ctx.db.get(args.strandId),
    ])

    const track = strand ? await ctx.db.get(strand.trackId) : null

    return majors.map((major) => ({
      ...major,
      strand,
      track,
    }))
  },
})

export const getByTrack = query({
  args: { trackId: v.id('tracks') },
  handler: async (ctx, args) => {
    // Get strands and track in parallel
    const [strands, track] = await Promise.all([
      ctx.db
        .query('strands')
        .withIndex('by_track', (q) => q.eq('trackId', args.trackId))
        .collect(),
      ctx.db.get(args.trackId),
    ])

    // Get all majors for these strands in parallel
    const allMajors = await Promise.all(
      strands.map(async (strand) => {
        const majors = await ctx.db
          .query('majors')
          .withIndex('by_strand', (q) => q.eq('strandId', strand._id))
          .collect()

        // Attach strand info to each major
        return majors.map((major) => ({
          ...major,
          strand,
          track,
        }))
      })
    )

    // Flatten the array
    return allMajors.flat()
  },
})
