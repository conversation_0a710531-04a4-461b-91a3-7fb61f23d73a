import { z } from 'zod'

// Validation schema for forms
export const sectionSchema = z.object({
  name: z.string().min(1, 'Section name is required'),
  gradeLevel: z.number().int().min(7).max(12, 'Grade level must be between 7-12'),
  academicYearId: z.string().min(1, 'Academic year is required'),
  adviserId: z.string().min(1, 'Adviser is required'),
  trackId: z.string().optional(),
  strandId: z.string().optional(),
  majorId: z.string().optional(),
  maleCount: z.number().int().min(0, 'Male count must be non-negative'),
  femaleCount: z.number().int().min(0, 'Female count must be non-negative'),
  isActive: z.boolean(),
})
