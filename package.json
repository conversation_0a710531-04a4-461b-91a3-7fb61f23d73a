{"name": "enrollr", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth/core": "^0.39.1", "@convex-dev/auth": "^0.0.87", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-form": "^1.12.1", "@tanstack/react-router": "^1.120.13", "@tanstack/react-router-devtools": "^1.120.13", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.6", "date-fns": "^4.1.0", "lucide-react": "^0.513.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "zod": "^3.25.51", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/eslint-plugin-router": "^1.115.0", "@tanstack/router-plugin": "^1.120.13", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "babel-plugin-react-compiler": "19.1.0-rc.2", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "6.0.0-rc1", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}