import { query } from './_generated/server'
import { v } from 'convex/values'

export const getAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query('strands').collect()
  },
})

export const getById = query({
  args: { id: v.id('strands') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})

export const getByTrack = query({
  args: { trackId: v.id('tracks') },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('strands')
      .withIndex('by_track', (q) => q.eq('trackId', args.trackId))
      .collect()
  },
})

export const getByTrackWithDetails = query({
  args: { trackId: v.id('tracks') },
  handler: async (ctx, args) => {
    const [strands, track] = await Promise.all([
      ctx.db
        .query('strands')
        .withIndex('by_track', (q) => q.eq('trackId', args.trackId))
        .collect(),
      ctx.db.get(args.trackId),
    ])

    return strands.map((strand) => ({
      ...strand,
      track,
    }))
  },
})
