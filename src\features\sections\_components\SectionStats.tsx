import { UsersIcon, BookOpenIcon, GraduationCapIcon, UserIcon } from 'lucide-react'
import { useQuery } from 'convex/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { api } from 'convex/_generated/api'
import { getTotalStudents } from '../lib/utils'

export function SectionStats() {
  const sections = useQuery(api.sections.getAll)

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Sections</CardTitle>
          <UsersIcon className="text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {sections?.filter((s) => s.isActive).length || 0}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Junior High</CardTitle>
          <BookOpenIcon className="text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {sections?.filter((s) => s.isActive && s.gradeLevel >= 7 && s.gradeLevel <= 10)
              .length || 0}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Senior High</CardTitle>
          <GraduationCapIcon className="text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {sections?.filter((s) => s.isActive && s.gradeLevel >= 11 && s.gradeLevel <= 12)
              .length || 0}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Students</CardTitle>
          <UserIcon className="text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {sections
              ?.filter((s) => s.isActive)
              .reduce((sum, section) => sum + getTotalStudents(section), 0) || 0}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
