import { UserCogIcon, BellRingIcon, LogOutIcon } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar'
import { Button } from '@/components/ui/Button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu'
import { useAuthActions } from '@convex-dev/auth/react'
import { useQuery } from 'convex/react'
import { api } from 'convex/_generated/api'

export function AppUser() {
  const { signOut } = useAuthActions()
  const user = useQuery(api.users.currentUser)
  if (!user) {
    return null
  }
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 px-2 py-1 rounded-full h-auto data-[state=open]:bg-accent data-[state=open]:text-accent-foreground"
        >
          <Avatar className="size-7 rounded-full">
            <AvatarImage src={user.image || ''} alt={user.name || 'User'} />
            <AvatarFallback className="rounded-full text-xs">
              {user.name?.charAt(0)?.toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div className="hidden sm:grid text-left text-xs leading-tight">
            <span className="truncate font-semibold">{user.name || 'User'}</span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="rounded-3xl" side="bottom" align="center" sideOffset={8}>
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <UserCogIcon />
            Account
          </DropdownMenuItem>
          <DropdownMenuItem>
            <BellRingIcon />
            Notifications
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem variant="destructive" onClick={signOut}>
          <LogOutIcon />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
