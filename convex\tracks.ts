import { query } from './_generated/server'
import { v } from 'convex/values'

export const getAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query('tracks').collect()
  },
})

export const getById = query({
  args: { id: v.id('tracks') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})

export const getByName = query({
  args: { name: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('tracks')
      .withIndex('by_name', (q) => q.eq('name', args.name))
      .first()
  },
})
