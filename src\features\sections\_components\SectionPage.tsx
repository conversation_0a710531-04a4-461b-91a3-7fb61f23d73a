import * as React from 'react'
import { PlusIcon } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { SectionStats } from './SectionStats'
import { SectionTable } from './SectionTable'
import { SectionCreate } from './SectionCreate'
import { SectionUpdate } from './SectionUpdate'
import { SectionDelete } from './SectionDelete'
import type { SectionId } from '@/lib/types'

export function SectionPage() {
  const [selectedGradeLevel, setSelectedGradeLevel] = React.useState<number | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = React.useState(false)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = React.useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false)
  const [selectedSectionId, setSelectedSectionId] = React.useState<SectionId | null>(null)

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sections</h1>
          <p className="text-muted-foreground">
            Manage class sections for the current academic year
          </p>
        </div>
        <Button
          onClick={() => {
            setIsCreateDialogOpen(true)
          }}
        >
          <PlusIcon />
          Add Section
        </Button>
      </div>

      <SectionStats />

      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Search and filter sections</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="flex-1">
              <Input
                placeholder="Search sections, advisers, tracks..."
                value={''}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={selectedGradeLevel === null ? 'default' : 'outline'}
                onClick={() => setSelectedGradeLevel(null)}
                size="sm"
              >
                All Grades
              </Button>
              {[7, 8, 9, 10, 11, 12].map((grade) => (
                <Button
                  key={grade}
                  variant={selectedGradeLevel === grade ? 'default' : 'outline'}
                  onClick={() => setSelectedGradeLevel(grade)}
                  size="sm"
                >
                  Grade {grade}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <SectionTable
        onEdit={(sectionId: SectionId) => {
          setSelectedSectionId(sectionId)
          setIsUpdateDialogOpen(true)
        }}
        onDelete={(sectionId: SectionId) => {
          setSelectedSectionId(sectionId)
          setIsDeleteDialogOpen(true)
        }}
      />

      <SectionCreate open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} />

      {selectedSectionId && (
        <SectionUpdate
          sectionId={selectedSectionId}
          open={isUpdateDialogOpen}
          onOpenChange={(open: boolean) => {
            setIsUpdateDialogOpen(open)
          }}
        />
      )}

      {selectedSectionId && (
        <SectionDelete
          sectionId={selectedSectionId}
          open={isDeleteDialogOpen}
          onOpenChange={(open: boolean) => {
            setIsDeleteDialogOpen(open)
          }}
        />
      )}
    </div>
  )
}
