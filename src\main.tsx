import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ConvexReactClient } from 'convex/react'
import { ConvexAuthProvider } from '@convex-dev/auth/react'
import { createRouter, RouterProvider } from '@tanstack/react-router'
import { ThemeProvider, ToasterProvider } from '@/components/providers'
import { routeTree } from '@/routeTree.gen'
import '@/styles.css'

const router = createRouter({ routeTree })
const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string)

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ConvexAuthProvider client={convex}>
      <ThemeProvider>
        <ToasterProvider>
          <RouterProvider router={router} />
        </ToasterProvider>
      </ThemeProvider>
    </ConvexAuthProvider>
  </StrictMode>
)
