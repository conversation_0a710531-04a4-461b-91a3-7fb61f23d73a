import { query } from './_generated/server'
import { v } from 'convex/values'

export const getAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query('academicYears').collect()
  },
})

export const getActive = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('academicYears')
      .withIndex('by_active', (q) => q.eq('isActive', true))
      .first()
  },
})

export const getBySchoolYear = query({
  args: { schoolYear: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('academicYears')
      .withIndex('by_school_year', (q) => q.eq('schoolYear', args.schoolYear))
      .first()
  },
})

export const getById = query({
  args: { id: v.id('academicYears') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})
