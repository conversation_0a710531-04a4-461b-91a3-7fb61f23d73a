import { Toaster } from 'sonner'

export function ToasterProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <Toaster
        position="top-center"
        gap={8}
        richColors
        toastOptions={{
          duration: 3000,
          style: {
            alignItems: 'center',
            justifyContent: 'center',
            width: 'auto',
            maxWidth: '30rem',
            borderRadius: '0.75rem',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            fontSize: '0.875rem',
            fontWeight: '500',
            padding: '0.75rem 1rem',
            wordBreak: 'break-word',
          },
        }}
      />
    </>
  )
}
