import { defineSchema, defineTable } from 'convex/server'
import { v } from 'convex/values'
import { authTables } from '@convex-dev/auth/server'

const schema = defineSchema({
  ...authTables,

  academicYears: defineTable({
    schoolYear: v.string(),
    startDate: v.number(),
    endDate: v.number(),
    enrollmentStartDate: v.number(),
    enrollmentEndDate: v.number(),
    isActive: v.boolean(),
  })
    .index('by_active', ['isActive'])
    .index('by_school_year', ['schoolYear']),

  tracks: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
  }).index('by_name', ['name']),

  strands: defineTable({
    name: v.string(),
    trackId: v.id('tracks'),
    description: v.optional(v.string()),
  }).index('by_track', ['trackId']),

  majors: defineTable({
    name: v.string(),
    strandId: v.id('strands'),
    description: v.optional(v.string()),
  }).index('by_strand', ['strandId']),

  students: defineTable({
    lrn: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    suffix: v.optional(v.string()),
    birthDate: v.string(),
    gender: v.union(v.literal('male'), v.literal('female')),
    address: v.object({
      barangay: v.string(),
      municipality: v.string(),
      province: v.string(),
    }),
    guardianInfo: v.object({
      guardianFirstName: v.string(),
      guardianMiddleName: v.optional(v.string()),
      guardianLastName: v.string(),
      relationship: v.string(),
      contactNumber: v.string(),
      email: v.optional(v.string()),
    }),
    schoolStatus: v.union(
      v.literal('active'),
      v.literal('inactive'),
      v.literal('transferred'),
      v.literal('graduated'),
      v.literal('dropped')
    ),
    isCCTRecipient: v.boolean(),
  })
    .index('by_lrn', ['lrn'])
    .index('by_status', ['schoolStatus'])
    .index('by_name', ['lastName', 'firstName']),

  teachers: defineTable({
    teacherId: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    middleName: v.optional(v.string()),
    email: v.optional(v.string()),
    contactNumber: v.optional(v.string()),
    position: v.optional(v.string()),
    department: v.optional(v.string()),
    isActive: v.boolean(),
  })
    .index('by_teacher_id', ['teacherId'])
    .index('by_active_status', ['isActive'])
    .index('by_name', ['lastName', 'firstName']),

  sections: defineTable({
    name: v.string(),
    gradeLevel: v.number(),
    academicYearId: v.id('academicYears'),
    adviserId: v.id('teachers'),
    trackId: v.optional(v.id('tracks')),
    strandId: v.optional(v.id('strands')),
    majorId: v.optional(v.id('majors')),
    maleCount: v.number(),
    femaleCount: v.number(),
    isActive: v.boolean(),
  })
    .index('by_grade_level', ['gradeLevel'])
    .index('by_academic_year', ['academicYearId'])
    .index('by_adviser', ['adviserId'])
    .index('by_track_strand', ['trackId', 'strandId']),

  enrollments: defineTable({
    studentId: v.id('students'),
    academicYearId: v.id('academicYears'),
    gradeLevel: v.number(),
    sectionId: v.optional(v.id('sections')),
    trackId: v.optional(v.id('tracks')),
    strandId: v.optional(v.id('strands')),
    majorId: v.optional(v.id('majors')),
    status: v.union(
      v.literal('pending'),
      v.literal('enrolled'),
      v.literal('rejected'),
      v.literal('completed')
    ),
    enrollmentDate: v.number(),
    approvedDate: v.optional(v.number()),
    approvedBy: v.optional(v.id('users')),
    documents: v.array(
      v.object({
        type: v.union(
          v.literal('Birth Certificate'),
          v.literal('Form 138'),
          v.literal('Good Moral Certificate')
        ),
        verified: v.boolean(),
        verifiedBy: v.optional(v.id('users')),
        verifiedAt: v.optional(v.number()),
        notes: v.optional(v.string()),
        required: v.boolean(),
      })
    ),
    notes: v.optional(v.string()),
  }).index('by_student_year', ['studentId', 'academicYearId']),
})

export default schema
