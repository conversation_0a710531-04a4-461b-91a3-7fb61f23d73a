import { createRootRoute, Link, Outlet, HeadContent } from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import { Spinner } from '@/components/ui/Spinner'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      { name: 'viewport', content: 'width=device-width, initial-scale=1.0' },
      { name: 'author', content: 'Enrollr Team' },
      { name: 'theme-color', content: '#3b82f6' },
    ],
  }),
  component: RootComponent,
  errorComponent: ({ error, reset }) => {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="max-w-md w-full p-6 bg-white border border-red-200 rounded-lg shadow-lg">
          <h1 className="text-2xl font-bold text-red-800 mb-4">Application Error</h1>
          <p className="text-red-600 mb-4">{error.message}</p>
          <div className="flex gap-2">
            <button
              onClick={reset}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Try again
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              Reload page
            </button>
          </div>
        </div>
      </div>
    )
  },
  pendingComponent: () => (
    <div className="min-h-screen flex items-center justify-center">
      <Spinner variant="circle" size={48} className="text-primary" />
    </div>
  ),
  notFoundComponent: () => (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
        <p className="text-gray-600 mb-4">Page not found</p>
        <Link to="/" className="text-blue-600 hover:underline">
          Go back home
        </Link>
      </div>
    </div>
  ),
})

function RootComponent() {
  return (
    <>
      <HeadContent />
      <Outlet />
      <TanStackRouterDevtools />
    </>
  )
}
