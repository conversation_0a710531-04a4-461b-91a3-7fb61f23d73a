import { query } from './_generated/server'
import { v } from 'convex/values'

export const getAll = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query('teachers').collect()
  },
})

export const getActive = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query('teachers')
      .withIndex('by_active_status', (q) => q.eq('isActive', true))
      .collect()
  },
})

export const getById = query({
  args: { id: v.id('teachers') },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id)
  },
})

export const getByTeacherId = query({
  args: { teacherId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('teachers')
      .withIndex('by_teacher_id', (q) => q.eq('teacherId', args.teacherId))
      .first()
  },
})

export const getAvailableForAdvising = query({
  args: { excludeSectionId: v.optional(v.id('sections')) },
  handler: async (ctx, args) => {
    // Get all active teachers
    const activeTeachers = await ctx.db
      .query('teachers')
      .withIndex('by_active_status', (q) => q.eq('isActive', true))
      .collect()

    // Get all sections to find which teachers are already assigned as advisers
    const allSections = await ctx.db.query('sections').collect()

    // Create a set of adviser IDs that are already assigned to sections
    // Exclude the current section being edited if excludeSectionId is provided
    const assignedAdviserIds = new Set(
      allSections
        .filter(
          (section) =>
            section.adviserId && (!args.excludeSectionId || section._id !== args.excludeSectionId)
        )
        .map((section) => section.adviserId)
    )

    // Filter out teachers who are already assigned as advisers to other sections
    return activeTeachers.filter((teacher) => !assignedAdviserIds.has(teacher._id))
  },
})
