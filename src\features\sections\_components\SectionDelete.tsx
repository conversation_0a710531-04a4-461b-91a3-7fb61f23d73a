import * as React from 'react'
import { Trash2Icon, AlertTriangleIcon, CircleAlertIcon } from 'lucide-react'
import { useQuery, useMutation } from 'convex/react'
import { toast } from 'sonner'
import { But<PERSON> } from '@/components/ui/Button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/Dialog'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/Alert'
import { Spinner } from '@/components/ui/Spinner'
import { Badge } from '@/components/ui/Badge'
import { api } from 'convex/_generated/api'
import type { SectionId } from '@/lib/types'
import { getTotalStudents } from '../lib/utils'

export function SectionDelete({
  sectionId,
  open,
  onOpenChange,
}: {
  sectionId: SectionId
  open: boolean
  onOpenChange: (open: boolean) => void
}) {
  const [isLoading, setIsLoading] = React.useState(false)
  const section = useQuery(api.sections.getById, { id: sectionId })
  const deleteSection = useMutation(api.sections.remove)
  if (!section) return null

  const handleDelete = async () => {
    try {
      setIsLoading(true)
      await deleteSection({ id: section._id })
      toast.success('Section deleted successfully')
      onOpenChange(false)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to delete section')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangleIcon className="text-destructive" />
            Delete Section
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to permanently delete <Badge>{section.name}</Badge> ?
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          {getTotalStudents(section) > 0 && (
            <Alert variant="destructive">
              <AlertTriangleIcon />
              <AlertTitle>Warning: Section has enrolled students</AlertTitle>
              <AlertDescription>
                <p>
                  This section currently has {getTotalStudents(section)} enrolled student
                  {getTotalStudents(section) !== 1 ? 's' : ''}. Deleting this section may affect student records
                  and enrollment data.
                </p>
                <p>Consider transferring students to another section before deletion.</p>
              </AlertDescription>
            </Alert>
          )}

          <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
            <CircleAlertIcon className="text-orange-600 dark:text-orange-400" />
            <AlertTitle className="text-orange-800 dark:text-orange-200">
              This action cannot be undone
            </AlertTitle>
            <AlertDescription className="text-orange-700 dark:text-orange-300">
              The section will be permanently removed from the system. All associated data and
              relationships will be lost.
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button type="button" variant="destructive" onClick={handleDelete} disabled={isLoading}>
            {isLoading ? (
              <>
                <Spinner variant="circle" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2Icon />
                Delete
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
